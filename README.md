# 🐺 NestJS 全栈开发学习路线 

## 📚 学习目录

### 🚀 入门指南
- `📂 1.hello-world` - 创建第一个NestJS应用
- `📂 2.controller` - 控制器与路由基础
- `📂 3.provider` - 依赖注入与服务提供者

### 🛠 核心模块
- `📂 4.middleware` - 中间件开发实践 🛡
- `📂 5.exception-filter` - 异常处理机制 ⚠
- `📂 6.pipe-guard` - 管道与守卫开发 🔐
- `📂 7.interceptor` - 拦截器实战 ✨

### 🔒 安全专题
- `📂 8.access_token` - JWT认证实现 🔑 
- `📂 9.refresh_token` - 令牌刷新机制 🔄
- `📂 10.role-guard` - 基于角色的权限控制 👮

### 🗄 数据库集成
- `📂 11.typeorm-mysql` - TypeORM MySQL集成 🐬
- `📂 12.entity-relation` - 实体关系映射 ⛓
- `📂 13.query-builder` - 复杂查询构建 🔍

### 🌐 高级特性
- `📂 14.websocket` - 实时通信模块 📡 
- `📂 15.file-upload` - 文件上传服务 📤
- `📂 16.config-module` - 多环境配置管理 ⚙
- `📂 17.unit-test` - 单元测试实践 ✅

## 🚦 快速开始
```bash
# 安装依赖
🍉 npm install

# 开发模式
🚢 npm run start:dev

# 生产构建
🏗 npm run build