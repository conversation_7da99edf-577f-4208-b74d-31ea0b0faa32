<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="https://unpkg.com/axios@0.24.0/dist/axios.min.js"></script>
  </head>
  <body>
    <input id="fileInput" type="file" multiple />
    <script>
      const fileInput = document.querySelector('#fileInput');

      async function formData() {
        const data = new FormData();
        data.set('name', '光');
        data.set('age', 20);
        data.set('aaa', fileInput.files[0]);

        const res = await axios.post('http://localhost:3001/aaa', data);
        console.log(res);
      }
      async function formData2() {
        const data = new FormData();
        data.set('name', '光');
        data.set('age', 20);
        [...fileInput.files].forEach((item) => {
          data.append('bbb', item);
        });

        const res = await axios.post('http://localhost:3001/bbb', data, {
          headers: { 'content-type': 'multipart/form-data' },
        });
        console.log(res);
      }

      fileInput.onchange = formData2;
    </script>
  </body>
</html>
