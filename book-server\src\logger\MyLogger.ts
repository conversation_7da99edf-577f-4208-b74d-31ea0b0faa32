import { Injectable, LoggerService, LogLevel } from '@nestjs/common';

@Injectable()
export class LoggerTest implements LoggerService {
  log(message: string, context: string) {
    console.log(`---log---[${context}]---`, message);
  }

  error(message: string, context: string) {
    console.log(`---error---[${context}]---`, message);
  }

  warn(message: string, context: string) {
    console.log(`---warn---[${context}]---`, message);
  }
}
