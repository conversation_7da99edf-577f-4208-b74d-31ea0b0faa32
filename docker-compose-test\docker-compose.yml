services:
  nest-app:
    build:
      context: ./
      dockerfile: ./Dockerfile
    depends_on:
      - mysql-container
      - redis-container
    ports:
      - '3000:3000'
  mysql-container:
    image: mysql
    ports:
      - '3306:3306'
    volumes:
      - D:\docker-test\mysql:/var/lib/mysql
    environment:
      MYSQL_DATABASE: root
      MYSQL_ROOT_PASSWORD: asd.12345
  redis-container:
    image: redis
    ports:
      - '6379:6379'
    volumes:
      - D:\study\docker-redis:/data
