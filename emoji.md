
# ====常用颜文字====
# 🎉 :tada: Begin a project.                                开始一个项目
# 🚧 :construction: Work in progress.                       工作进行中
# ✨ :sparkles: Introduce new features.                     添加新功能
# 📝 :memo: Add or update documentation.                    增加或更新文档
# 🔧 :wrench: Add or update configuration files.            增加/更新配置文件
# 💄 :lipstick: Add or update the UI and style files.       增加/更新UI和样式文件
# 💡 :bulb: Add or update comments in source code.          增加/更新源代码中的注释
# 🚚 :truck: Move or rename resources (e.g.: files, paths, routes). 移动/重命名文件/路径
# 🔥 :fire: Remove code or files.                           移除代码/文件
# 🐛 :bug: Fix a bug.                                       修复bug
# 🚑️ :ambulance: Critical hotfix.                           紧急修复
# ⏪️ :rewind: Revert changes.                               回退
# ✏️ :pencil2: Fix typos.                                   修改错别字
# 🔀 :twisted_rightwards_arrows: Merge branches.            合并分支
# ⚡️ :zap: Improve performance.                             性能优化
# 🎨 :art: Improve structure / format of the code.          改进代码结构或格式
# ✅ :white_check_mark: Add, update, or pass tests.         增加/更新测试用例
# 📦️ :package: Add or update compiled files or packages.    增加/更新编译后文件/包
# 🙈 :see_no_evil: Add or update a .gitignore file.         增加/更新.gitignore文件
# 📄 :page_facing_up: Add or update license.                增加/更新LICENSE
# 🔊 :loud_sound: Add or update logs.                       增加/更新日志
# 🔇 :mute: Remove logs.                                    移除日志
# 👥 :busts_in_silhouette: Add or update contributor(s).    增加/更新贡献者
# ♻️ :recycle: Refactor code.                               重构代码
# 📈 :chart_with_upwards_trend: Add or update analytics or track code. 增加/更新分析/跟踪代码
# 🚸 :children_crossing: Improve user experience / usability. 增强用户体验/可用性
# 🛂 :passport_control: Work on code related to authorization, roles and permissions. 处理与授权、身份和权限相关的代码

# ====附加颜文字====
# ➕ :heavy_plus_sign: Add a dependency.                            增加一个依赖
# ➖ :heavy_minus_sign: Remove a dependency.                        移除一个依赖
# ⬇️ :arrow_down: Downgrade dependencies.                           降级依赖
# ⬆️ :arrow_up: Upgrade dependencies.                               升级依赖
# 📌 :pushpin: Pin dependencies to specific versions.               固定依赖到特定版本
# 🚀 :rocket: Deploy stuff.                                         部署功能
# 👽️ :alien: Update code due to external API changes.               由于外部API更改而更新代码
# 🔖 :bookmark: Release / Version tags.                             发布/版本标签
# 🔍️ :mag: Improve SEO.                                             提高SEO
# 🥅 :goal_net: Catch errors.                                       捕捉错误
# 🔒️ :lock: Fix security issues.                                    修复安全问题
# 🗑️ :wastebasket: Deprecate code that needs to be cleaned up.      废弃代码,需要清理
# ⚰️ :coffin: Remove dead code.                                     移除无用代码
# 🧪 :test_tube: Add a failing test.                                增加一个失败的测试
# 🏷️ :label: Add or update types.                                   增加/更新类型
# 💫 :dizzy: Add or update animations and transitions.              增加/更新动画和过渡
# 🚩 :triangular_flag_on_post: Add, update, or remove feature flags.增加/更新/移除功能标识
# 🔨 :hammer: Add or update development scripts.                    增加/更新开发脚本
# 👔 :necktie: Add or update business logic                         增加/更新业务逻辑
# 🍱 :bento: Add or update assets.                                  增加/更新assets
# 🌱 :seedling: Add or update seed files.                           增加/更新种子文件
# 📸 :camera_flash: Add or update snapshots.                        增加/更新快照
# 👷 :construction_worker: Add or update CI build system.           增加/更新CI构建系统
# 💚 :green_heart: Fix CI Build.                                    修复CI构建系统
# 💩 :poop: Write bad code that needs to be improved.               代码很烂需要改进
# 🚨 :rotating_light: Fix compiler / linter warnings.               修复compiler/linter的警告
# ♿️ :wheelchair: Improve accessibility.                            改善可访问性
# ⚗️ :alembic: Perform experiments.                                 进行实验
# 💥 :boom: Introduce breaking changes.                             引入破坏性改变
# 🗃️ :card_file_box: Perform database related changes.              执行数据库相关的改变
# 🏗️ :building_construction: Make architectural changes.            执行架构层次的改变
# 🩹 :adhesive_bandage: Simple fix for a non-critical issue.        非关键问题的简单修复
# 🧐 :monocle_face: Data exploration/inspection.                    数据探查/检查

# ====其他颜文字====
# 🌐 :globe_with_meridians: Internationalization and localization.  国际化与本地化
# 🍻 :beers: Write code drunkenly.                                  醉酒写码
# 💬 :speech_balloon: Add or update text and literals.              增加/更新文本和文字
# 📱 :iphone: Work on responsive design.                             致力于响应式设计
# 🥚 :egg: Add or update an easter egg.                             增加/更新复活节蛋
# 🤡 :clown_face: Mock things.                                      
