{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/redis": ["libs/redis/src"], "@app/redis/*": ["libs/redis/src/*"], "@app/prisma": ["libs/prisma/src"], "@app/prisma/*": ["libs/prisma/src/*"], "@app/email": ["libs/email/src"], "@app/email/*": ["libs/email/src/*"], "@app/common": ["libs/common/src"], "@app/common/*": ["libs/common/src/*"], "@app/excel": ["libs/excel/src"], "@app/excel/*": ["libs/excel/src/*"]}}}