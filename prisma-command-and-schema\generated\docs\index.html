
<!DOCTYPE html>
<html lang="en">
  <head>
    
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <title>Prisma Generated Docs</title>
    <link rel="stylesheet" href="styles/main.css" />
    
  </head>
  <body class="bg-gray-200">
    <div class="flex min-h-screen">
      <div
        class="sticky top-0 w-1/5 flex-shrink-0 h-screen p-4 px-6 mr-4 overflow-auto bg-white mac-h-screen"
      >
        <div class="mb-8">
          
    <svg
      width="109"
      height="40"
      viewBox="0 0 109 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style="cursor: pointer;"
    >
      <g clip-path="url(#clip0)">
      <path
          d="M33.7753 30.4749L19.6557 1.25993C19.4803 0.900386 19.2084 0.593746 18.8687 0.372468C18.529 0.151189 18.1343 0.0235052 17.7262 0.00293118C17.3174 -0.0237544 16.909 0.0592759 16.5452 0.242992C16.1815 0.426708 15.8764 0.704076 15.6632 1.04493L0.349 25.2119C0.116122 25.5768 -0.00515381 25.9989 -0.000609227 26.4286C0.00393536 26.8583 0.134112 27.2778 0.374658 27.6379L7.86191 38.9349C8.14517 39.359 8.56155 39.6825 9.04894 39.8571C9.53633 40.0317 10.0686 40.0482 10.5664 39.9039L32.2943 33.6419C32.6214 33.5487 32.9242 33.3882 33.1822 33.1712C33.4402 32.9541 33.6473 32.6857 33.7897 32.3839C33.93 32.0837 34.0015 31.7574 33.9992 31.4275C33.9969 31.0976 33.9208 30.7722 33.7763 30.4739L33.7753 30.4749ZM30.6141 31.7279L12.1767 37.0399C11.6143 37.2029 11.0744 36.7279 11.1914 36.1749L17.7785 5.44393C17.9017 4.86893 18.7166 4.77793 18.9742 5.30993L31.1684 30.5399C31.2223 30.6524 31.2505 30.7748 31.2511 30.8989C31.2518 31.023 31.2247 31.1458 31.172 31.2587C31.1192 31.3716 31.0419 31.4721 30.9454 31.5531C30.8489 31.6341 30.7355 31.6937 30.6131 31.7279H30.6141Z"
      fill="black"
    ></path>
      </g>
  </svg>

    
        </div>
        
        <div>
          <h5 class="mb-2 font-bold"><a href="#models">Models</a></h5>
          <ul class="mb-2 ml-1">
              
            <li class="mb-4">
                
    <div class="font-semibold text-gray-700">
      <a href="#model-post">post</a>
    </div>
   
                  <div class="mt-1 ml-2">
                    <div class="mb-1 font-medium text-gray-600"><a href="#model-post-fields">Fields</a></div>
                      <ul class="pl-3 ml-1 border-l-2 border-gray-400">
                      <li><a href="#model-post-id">id</a></li><li><a href="#model-post-title">title</a></li><li><a href="#model-post-content">content</a></li><li><a href="#model-post-tag">tag</a></li><li><a href="#model-post-published">published</a></li><li><a href="#model-post-authorId">authorId</a></li><li><a href="#model-post-user">user</a></li>
                      </ul>
                  </div>
                  <div class="mt-2 ml-2">
                    <div class="mb-1 font-medium text-gray-600"><a href="#model-post-operations">Operations</a></div>
                    <ul class="pl-3 ml-1 border-l-2 border-gray-400">
                    <li><a href="#model-post-findUnique">findUnique</a></li><li><a href="#model-post-findFirst">findFirst</a></li><li><a href="#model-post-findMany">findMany</a></li><li><a href="#model-post-create">create</a></li><li><a href="#model-post-delete">delete</a></li><li><a href="#model-post-update">update</a></li><li><a href="#model-post-deleteMany">deleteMany</a></li><li><a href="#model-post-updateMany">updateMany</a></li><li><a href="#model-post-upsert">upsert</a></li>
                    </ul>
                  </div>
            </li>
              
            <li class="mb-4">
                
    <div class="font-semibold text-gray-700">
      <a href="#model-user">user</a>
    </div>
   
                  <div class="mt-1 ml-2">
                    <div class="mb-1 font-medium text-gray-600"><a href="#model-user-fields">Fields</a></div>
                      <ul class="pl-3 ml-1 border-l-2 border-gray-400">
                      <li><a href="#model-user-id">id</a></li><li><a href="#model-user-email">email</a></li><li><a href="#model-user-name">name</a></li><li><a href="#model-user-post">post</a></li>
                      </ul>
                  </div>
                  <div class="mt-2 ml-2">
                    <div class="mb-1 font-medium text-gray-600"><a href="#model-user-operations">Operations</a></div>
                    <ul class="pl-3 ml-1 border-l-2 border-gray-400">
                    <li><a href="#model-user-findUnique">findUnique</a></li><li><a href="#model-user-findFirst">findFirst</a></li><li><a href="#model-user-findMany">findMany</a></li><li><a href="#model-user-create">create</a></li><li><a href="#model-user-delete">delete</a></li><li><a href="#model-user-update">update</a></li><li><a href="#model-user-deleteMany">deleteMany</a></li><li><a href="#model-user-updateMany">updateMany</a></li><li><a href="#model-user-upsert">upsert</a></li>
                    </ul>
                  </div>
            </li>
              
            </ul>
          <h5 class="mt-12 mb-2 font-bold"><a href="#types">Types</a></h5>
          <ul class="mb-2 ml-1">
            <li class="mb-4">
              <div class="font-semibold text-gray-700">
                <a href="#input-types">Input Types</a>
              </div>
              <ul class="pl-3 ml-1 border-l-2 border-gray-400">
              <li><a href="#type-inputType-postWhereInput">postWhereInput</a></li><li><a href="#type-inputType-postOrderByWithRelationInput">postOrderByWithRelationInput</a></li><li><a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a></li><li><a href="#type-inputType-postOrderByWithAggregationInput">postOrderByWithAggregationInput</a></li><li><a href="#type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput</a></li><li><a href="#type-inputType-userWhereInput">userWhereInput</a></li><li><a href="#type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput</a></li><li><a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a></li><li><a href="#type-inputType-userOrderByWithAggregationInput">userOrderByWithAggregationInput</a></li><li><a href="#type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput</a></li><li><a href="#type-inputType-postCreateInput">postCreateInput</a></li><li><a href="#type-inputType-postUncheckedCreateInput">postUncheckedCreateInput</a></li><li><a href="#type-inputType-postUpdateInput">postUpdateInput</a></li><li><a href="#type-inputType-postUncheckedUpdateInput">postUncheckedUpdateInput</a></li><li><a href="#type-inputType-postCreateManyInput">postCreateManyInput</a></li><li><a href="#type-inputType-postUpdateManyMutationInput">postUpdateManyMutationInput</a></li><li><a href="#type-inputType-postUncheckedUpdateManyInput">postUncheckedUpdateManyInput</a></li><li><a href="#type-inputType-userCreateInput">userCreateInput</a></li><li><a href="#type-inputType-userUncheckedCreateInput">userUncheckedCreateInput</a></li><li><a href="#type-inputType-userUpdateInput">userUpdateInput</a></li><li><a href="#type-inputType-userUncheckedUpdateInput">userUncheckedUpdateInput</a></li><li><a href="#type-inputType-userCreateManyInput">userCreateManyInput</a></li><li><a href="#type-inputType-userUpdateManyMutationInput">userUpdateManyMutationInput</a></li><li><a href="#type-inputType-userUncheckedUpdateManyInput">userUncheckedUpdateManyInput</a></li><li><a href="#type-inputType-IntFilter">IntFilter</a></li><li><a href="#type-inputType-StringFilter">StringFilter</a></li><li><a href="#type-inputType-StringNullableFilter">StringNullableFilter</a></li><li><a href="#type-inputType-BoolFilter">BoolFilter</a></li><li><a href="#type-inputType-UserRelationFilter">UserRelationFilter</a></li><li><a href="#type-inputType-SortOrderInput">SortOrderInput</a></li><li><a href="#type-inputType-postCountOrderByAggregateInput">postCountOrderByAggregateInput</a></li><li><a href="#type-inputType-postAvgOrderByAggregateInput">postAvgOrderByAggregateInput</a></li><li><a href="#type-inputType-postMaxOrderByAggregateInput">postMaxOrderByAggregateInput</a></li><li><a href="#type-inputType-postMinOrderByAggregateInput">postMinOrderByAggregateInput</a></li><li><a href="#type-inputType-postSumOrderByAggregateInput">postSumOrderByAggregateInput</a></li><li><a href="#type-inputType-IntWithAggregatesFilter">IntWithAggregatesFilter</a></li><li><a href="#type-inputType-StringWithAggregatesFilter">StringWithAggregatesFilter</a></li><li><a href="#type-inputType-StringNullableWithAggregatesFilter">StringNullableWithAggregatesFilter</a></li><li><a href="#type-inputType-BoolWithAggregatesFilter">BoolWithAggregatesFilter</a></li><li><a href="#type-inputType-PostListRelationFilter">PostListRelationFilter</a></li><li><a href="#type-inputType-postOrderByRelationAggregateInput">postOrderByRelationAggregateInput</a></li><li><a href="#type-inputType-userCountOrderByAggregateInput">userCountOrderByAggregateInput</a></li><li><a href="#type-inputType-userAvgOrderByAggregateInput">userAvgOrderByAggregateInput</a></li><li><a href="#type-inputType-userMaxOrderByAggregateInput">userMaxOrderByAggregateInput</a></li><li><a href="#type-inputType-userMinOrderByAggregateInput">userMinOrderByAggregateInput</a></li><li><a href="#type-inputType-userSumOrderByAggregateInput">userSumOrderByAggregateInput</a></li><li><a href="#type-inputType-userCreateNestedOneWithoutPostInput">userCreateNestedOneWithoutPostInput</a></li><li><a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a></li><li><a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a></li><li><a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a></li><li><a href="#type-inputType-userUpdateOneRequiredWithoutPostNestedInput">userUpdateOneRequiredWithoutPostNestedInput</a></li><li><a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a></li><li><a href="#type-inputType-postCreateNestedManyWithoutUserInput">postCreateNestedManyWithoutUserInput</a></li><li><a href="#type-inputType-postUncheckedCreateNestedManyWithoutUserInput">postUncheckedCreateNestedManyWithoutUserInput</a></li><li><a href="#type-inputType-postUpdateManyWithoutUserNestedInput">postUpdateManyWithoutUserNestedInput</a></li><li><a href="#type-inputType-postUncheckedUpdateManyWithoutUserNestedInput">postUncheckedUpdateManyWithoutUserNestedInput</a></li><li><a href="#type-inputType-NestedIntFilter">NestedIntFilter</a></li><li><a href="#type-inputType-NestedStringFilter">NestedStringFilter</a></li><li><a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a></li><li><a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a></li><li><a href="#type-inputType-NestedIntWithAggregatesFilter">NestedIntWithAggregatesFilter</a></li><li><a href="#type-inputType-NestedFloatFilter">NestedFloatFilter</a></li><li><a href="#type-inputType-NestedStringWithAggregatesFilter">NestedStringWithAggregatesFilter</a></li><li><a href="#type-inputType-NestedStringNullableWithAggregatesFilter">NestedStringNullableWithAggregatesFilter</a></li><li><a href="#type-inputType-NestedIntNullableFilter">NestedIntNullableFilter</a></li><li><a href="#type-inputType-NestedBoolWithAggregatesFilter">NestedBoolWithAggregatesFilter</a></li><li><a href="#type-inputType-userCreateWithoutPostInput">userCreateWithoutPostInput</a></li><li><a href="#type-inputType-userUncheckedCreateWithoutPostInput">userUncheckedCreateWithoutPostInput</a></li><li><a href="#type-inputType-userCreateOrConnectWithoutPostInput">userCreateOrConnectWithoutPostInput</a></li><li><a href="#type-inputType-userUpsertWithoutPostInput">userUpsertWithoutPostInput</a></li><li><a href="#type-inputType-userUpdateToOneWithWhereWithoutPostInput">userUpdateToOneWithWhereWithoutPostInput</a></li><li><a href="#type-inputType-userUpdateWithoutPostInput">userUpdateWithoutPostInput</a></li><li><a href="#type-inputType-userUncheckedUpdateWithoutPostInput">userUncheckedUpdateWithoutPostInput</a></li><li><a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a></li><li><a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a></li><li><a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput</a></li><li><a href="#type-inputType-postCreateManyUserInputEnvelope">postCreateManyUserInputEnvelope</a></li><li><a href="#type-inputType-postUpsertWithWhereUniqueWithoutUserInput">postUpsertWithWhereUniqueWithoutUserInput</a></li><li><a href="#type-inputType-postUpdateWithWhereUniqueWithoutUserInput">postUpdateWithWhereUniqueWithoutUserInput</a></li><li><a href="#type-inputType-postUpdateManyWithWhereWithoutUserInput">postUpdateManyWithWhereWithoutUserInput</a></li><li><a href="#type-inputType-postScalarWhereInput">postScalarWhereInput</a></li><li><a href="#type-inputType-postCreateManyUserInput">postCreateManyUserInput</a></li><li><a href="#type-inputType-postUpdateWithoutUserInput">postUpdateWithoutUserInput</a></li><li><a href="#type-inputType-postUncheckedUpdateWithoutUserInput">postUncheckedUpdateWithoutUserInput</a></li><li><a href="#type-inputType-postUncheckedUpdateManyWithoutUserInput">postUncheckedUpdateManyWithoutUserInput</a></li>
              </ul>
            </li>
            <li class="mb-4">
              <div class="font-semibold text-gray-700">
                <a href="#output-types">Output Types</a>
              </div>
              <ul class="pl-3 ml-1 border-l-2 border-gray-400">
              <li><a href="#type-outputType-post">post</a></li><li><a href="#type-outputType-user">user</a></li><li><a href="#type-outputType-AggregatePost">AggregatePost</a></li><li><a href="#type-outputType-PostGroupByOutputType">PostGroupByOutputType</a></li><li><a href="#type-outputType-AggregateUser">AggregateUser</a></li><li><a href="#type-outputType-UserGroupByOutputType">UserGroupByOutputType</a></li><li><a href="#type-outputType-AffectedRowsOutput">AffectedRowsOutput</a></li><li><a href="#type-outputType-PostCountAggregateOutputType">PostCountAggregateOutputType</a></li><li><a href="#type-outputType-PostAvgAggregateOutputType">PostAvgAggregateOutputType</a></li><li><a href="#type-outputType-PostSumAggregateOutputType">PostSumAggregateOutputType</a></li><li><a href="#type-outputType-PostMinAggregateOutputType">PostMinAggregateOutputType</a></li><li><a href="#type-outputType-PostMaxAggregateOutputType">PostMaxAggregateOutputType</a></li><li><a href="#type-outputType-UserCountOutputType">UserCountOutputType</a></li><li><a href="#type-outputType-UserCountAggregateOutputType">UserCountAggregateOutputType</a></li><li><a href="#type-outputType-UserAvgAggregateOutputType">UserAvgAggregateOutputType</a></li><li><a href="#type-outputType-UserSumAggregateOutputType">UserSumAggregateOutputType</a></li><li><a href="#type-outputType-UserMinAggregateOutputType">UserMinAggregateOutputType</a></li><li><a href="#type-outputType-UserMaxAggregateOutputType">UserMaxAggregateOutputType</a></li>
              </ul>
            </li>
          </ul>
        </div>
    
      </div>
      <div class="w-full p-4 bg-white overflow-x-hidden">
        
        <div class="mb-8">
          <h1 class="text-3xl text-gray-800" id="models">Models</h1>
            
            <div class="px-4 mb-4">
              <h2 class="text-2xl" id="model-post">post</h2>
              
              
              <div class="px-4 mt-4">
                <h3 class="mb-2 text-xl" id="model-post-fields">Fields</h3>
                <div class="px-2 mb-4">
                  <table class="table-auto">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Attributes</th>
                        <th class="px-4 py-2 border">Required</th>
                        <th class="px-4 py-2 border">Comment</th>
                      </tr>
                    </thead>
                    <tbody>
                    
    <tr id="model-post-id">
      <td class="px-4 py-2 border">
       id 
      </td>
      <td class="px-4 py-2 border">
       Int
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li><strong>@id</strong></li><li><strong>@default(autoincrement())</strong></li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-post-title">
      <td class="px-4 py-2 border">
       title 
      </td>
      <td class="px-4 py-2 border">
       String
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-post-content">
      <td class="px-4 py-2 border">
       content 
      </td>
      <td class="px-4 py-2 border">
       String?
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        No
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-post-tag">
      <td class="px-4 py-2 border">
       tag 
      </td>
      <td class="px-4 py-2 border">
       String?
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        No
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-post-published">
      <td class="px-4 py-2 border">
       published 
      </td>
      <td class="px-4 py-2 border">
       Boolean
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li><strong>@default(false)</strong></li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-post-authorId">
      <td class="px-4 py-2 border">
       authorId 
      </td>
      <td class="px-4 py-2 border">
       Int
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-post-user">
      <td class="px-4 py-2 border">
       user 
      </td>
      <td class="px-4 py-2 border">
       <a href="#type-outputType-user">user</a>
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
                    </tbody>
                  </table>
                </div>
            </div>
            <hr class="my-8">
              <div class="px-4 mt-4">
                <h3 class="mb-2 text-xl" id="model-post-operations">Operations</h3>
                <div class="px-2 mb-4">
                  
                <div class="mt-4">
                  <h4 id="model-post-findUnique" class="mb-2 text-lg font-bold">findUnique</h4>
                  <p>Find zero or one Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Get one Post</span>
<span class="token keyword">const</span> post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">findUnique</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-findFirst" class="mb-2 text-lg font-bold">findFirst</h4>
                  <p>Find first Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Get one Post</span>
<span class="token keyword">const</span> post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">findFirst</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereInput">postWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          orderBy
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postOrderByWithRelationInput">postOrderByWithRelationInput[]</a> | <a href="#type-inputType-postOrderByWithRelationInput">postOrderByWithRelationInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          cursor
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          take
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          skip
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          distinct
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-PostScalarFieldEnum">PostScalarFieldEnum</a> | <a href="#type-inputType-PostScalarFieldEnum">PostScalarFieldEnum[]</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-findMany" class="mb-2 text-lg font-bold">findMany</h4>
                  <p>Find zero or more Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Get all Post</span>
<span class="token keyword">const</span> Post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">findMany</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token comment">// Get first 10 Post</span>
<span class="token keyword">const</span> Post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">findMany</span><span class="token punctuation">(</span><span class="token punctuation">{</span> <span class="token literal-property property">take</span><span class="token operator">:</span> <span class="token number">10</span> <span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereInput">postWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          orderBy
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postOrderByWithRelationInput">postOrderByWithRelationInput[]</a> | <a href="#type-inputType-postOrderByWithRelationInput">postOrderByWithRelationInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          cursor
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          take
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          skip
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          distinct
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-PostScalarFieldEnum">PostScalarFieldEnum</a> | <a href="#type-inputType-PostScalarFieldEnum">PostScalarFieldEnum[]</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  Yes</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-create" class="mb-2 text-lg font-bold">create</h4>
                  <p>Create one Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Create one Post</span>
<span class="token keyword">const</span> Post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">create</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... data to create a Post</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          data
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postCreateInput">postCreateInput</a> | <a href="#type-inputType-postUncheckedCreateInput">postUncheckedCreateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-delete" class="mb-2 text-lg font-bold">delete</h4>
                  <p>Delete one Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Delete one Post</span>
<span class="token keyword">const</span> Post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">delete</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... filter to delete one Post</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span></code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-update" class="mb-2 text-lg font-bold">update</h4>
                  <p>Update one Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Update one Post</span>
<span class="token keyword">const</span> post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">update</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide data here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          data
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postUpdateInput">postUpdateInput</a> | <a href="#type-inputType-postUncheckedUpdateInput">postUncheckedUpdateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-deleteMany" class="mb-2 text-lg font-bold">deleteMany</h4>
                  <p>Delete zero or more Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Delete a few Post</span>
<span class="token keyword">const</span> <span class="token punctuation">{</span> count <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">deleteMany</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereInput">postWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-AffectedRowsOutput">AffectedRowsOutput</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-updateMany" class="mb-2 text-lg font-bold">updateMany</h4>
                  <p>Update zero or one Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token keyword">const</span> <span class="token punctuation">{</span> count <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">updateMany</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide data here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span></code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          data
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postUpdateManyMutationInput">postUpdateManyMutationInput</a> | <a href="#type-inputType-postUncheckedUpdateManyInput">postUncheckedUpdateManyInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereInput">postWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-AffectedRowsOutput">AffectedRowsOutput</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-post-upsert" class="mb-2 text-lg font-bold">upsert</h4>
                  <p>Create or update one Post</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Update or create a Post</span>
<span class="token keyword">const</span> post <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>post<span class="token punctuation">.</span><span class="token function">upsert</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">create</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... data to create a Post</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">update</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... in case it already exists, update</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... the filter for the Post we want to update</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span></code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          create
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postCreateInput">postCreateInput</a> | <a href="#type-inputType-postUncheckedCreateInput">postUncheckedCreateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          update
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-postUpdateInput">postUpdateInput</a> | <a href="#type-inputType-postUncheckedUpdateInput">postUncheckedUpdateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-post">post</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    
                </div>
            </div>
          </div>
            <hr class="my-16">
            <div class="px-4 mb-4">
              <h2 class="text-2xl" id="model-user">user</h2>
              
              
              <div class="px-4 mt-4">
                <h3 class="mb-2 text-xl" id="model-user-fields">Fields</h3>
                <div class="px-2 mb-4">
                  <table class="table-auto">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Attributes</th>
                        <th class="px-4 py-2 border">Required</th>
                        <th class="px-4 py-2 border">Comment</th>
                      </tr>
                    </thead>
                    <tbody>
                    
    <tr id="model-user-id">
      <td class="px-4 py-2 border">
       id 
      </td>
      <td class="px-4 py-2 border">
       Int
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li><strong>@id</strong></li><li><strong>@default(autoincrement())</strong></li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-user-email">
      <td class="px-4 py-2 border">
       email 
      </td>
      <td class="px-4 py-2 border">
       String
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li><strong>@unique</strong></li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-user-name">
      <td class="px-4 py-2 border">
       name 
      </td>
      <td class="px-4 py-2 border">
       String?
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        No
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
    <tr id="model-user-post">
      <td class="px-4 py-2 border">
       post 
      </td>
      <td class="px-4 py-2 border">
       <a href="#type-outputType-post">post[]</a>
      </td>
      <td class="px-4 py-2 border">
        <ul>
          <li> - </li>
        </ul>
      </td>
      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
      <td class="px-4 py-2 border">
        -
      </td>
    </tr>
    
                    </tbody>
                  </table>
                </div>
            </div>
            <hr class="my-8">
              <div class="px-4 mt-4">
                <h3 class="mb-2 text-xl" id="model-user-operations">Operations</h3>
                <div class="px-2 mb-4">
                  
                <div class="mt-4">
                  <h4 id="model-user-findUnique" class="mb-2 text-lg font-bold">findUnique</h4>
                  <p>Find zero or one User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Get one User</span>
<span class="token keyword">const</span> user <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">findUnique</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-findFirst" class="mb-2 text-lg font-bold">findFirst</h4>
                  <p>Find first User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Get one User</span>
<span class="token keyword">const</span> user <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">findFirst</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereInput">userWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          orderBy
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput[]</a> | <a href="#type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          cursor
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          take
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          skip
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          distinct
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-UserScalarFieldEnum">UserScalarFieldEnum</a> | <a href="#type-inputType-UserScalarFieldEnum">UserScalarFieldEnum[]</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-findMany" class="mb-2 text-lg font-bold">findMany</h4>
                  <p>Find zero or more User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Get all User</span>
<span class="token keyword">const</span> User <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">findMany</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token comment">// Get first 10 User</span>
<span class="token keyword">const</span> User <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">findMany</span><span class="token punctuation">(</span><span class="token punctuation">{</span> <span class="token literal-property property">take</span><span class="token operator">:</span> <span class="token number">10</span> <span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereInput">userWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          orderBy
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput[]</a> | <a href="#type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          cursor
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          take
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          skip
                        </td>
                        <td class="px-4 py-2 border">
                        Int
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          distinct
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-UserScalarFieldEnum">UserScalarFieldEnum</a> | <a href="#type-inputType-UserScalarFieldEnum">UserScalarFieldEnum[]</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  Yes</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-create" class="mb-2 text-lg font-bold">create</h4>
                  <p>Create one User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Create one User</span>
<span class="token keyword">const</span> User <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">create</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... data to create a User</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          data
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userCreateInput">userCreateInput</a> | <a href="#type-inputType-userUncheckedCreateInput">userUncheckedCreateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-delete" class="mb-2 text-lg font-bold">delete</h4>
                  <p>Delete one User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Delete one User</span>
<span class="token keyword">const</span> User <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">delete</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... filter to delete one User</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span></code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-update" class="mb-2 text-lg font-bold">update</h4>
                  <p>Update one User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Update one User</span>
<span class="token keyword">const</span> user <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">update</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide data here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          data
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userUpdateInput">userUpdateInput</a> | <a href="#type-inputType-userUncheckedUpdateInput">userUncheckedUpdateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  No</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-deleteMany" class="mb-2 text-lg font-bold">deleteMany</h4>
                  <p>Delete zero or more User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Delete a few User</span>
<span class="token keyword">const</span> <span class="token punctuation">{</span> count <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">deleteMany</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereInput">userWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-AffectedRowsOutput">AffectedRowsOutput</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-updateMany" class="mb-2 text-lg font-bold">updateMany</h4>
                  <p>Update zero or one User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token keyword">const</span> <span class="token punctuation">{</span> count <span class="token punctuation">}</span> <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">updateMany</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide filter here</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... provide data here</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span></code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          data
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userUpdateManyMutationInput">userUpdateManyMutationInput</a> | <a href="#type-inputType-userUncheckedUpdateManyInput">userUncheckedUpdateManyInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereInput">userWhereInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         No 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-AffectedRowsOutput">AffectedRowsOutput</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    <hr class="my-4">
                <div class="mt-4">
                  <h4 id="model-user-upsert" class="mb-2 text-lg font-bold">upsert</h4>
                  <p>Create or update one User</p>
                  <div class="mb-2">
                    <pre
                      class="language-markup"
                    ><code class=" language-javascript"><span class="token comment">// Update or create a User</span>
<span class="token keyword">const</span> user <span class="token operator">=</span> <span class="token keyword">await</span> prisma<span class="token punctuation">.</span>user<span class="token punctuation">.</span><span class="token function">upsert</span><span class="token punctuation">(</span><span class="token punctuation">{</span>
  <span class="token literal-property property">create</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... data to create a User</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">update</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... in case it already exists, update</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">where</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token comment">// ... the filter for the User we want to update</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span></code></pre>
                  </div>
                  <h4 class="text-lg mb-2">Input</h4>
                  <table class="table-auto mb-2">
                    <thead>
                      <tr>
                        <th class="px-4 py-2 border">Name</th>
                        <th class="px-4 py-2 border">Type</th>
                        <th class="px-4 py-2 border">Required</th>
                      </tr>
                    </thead>
                    <tbody>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          where
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          create
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userCreateInput">userCreateInput</a> | <a href="#type-inputType-userUncheckedCreateInput">userUncheckedCreateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                      <tr>
                        <td class="px-4 py-2 border">
                          update
                        </td>
                        <td class="px-4 py-2 border">
                        <a href="#type-inputType-userUpdateInput">userUpdateInput</a> | <a href="#type-inputType-userUncheckedUpdateInput">userUncheckedUpdateInput</a>
                        </td>
                        <td class="px-4 py-2 border">
                         <strong>Yes</strong> 
                        </td>
                      </tr>
                      
                    </tbody>
                  </table>
                  <h4 class="text-lg mb-2">Output</h4>
                  <div><strong>Type: </strong> <a href="#type-outputType-user">user</a></div>
                  <div><strong>Required: </strong>
                  Yes</div>
                  <div><strong>List: </strong>
                  No</div>
              </div>
    
                </div>
            </div>
          </div>
            
        </div>
    
        <div>
    <h1 class="text-3xl" id="types">Types</h1>
        <div>
          <div class="ml-4">
            <h3 class="mb-2 text-2xl font-normal" id="input-types">Input Types</h3>
            <div class="ml-4">
              
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postWhereInput">postWhereInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a> | <a href="#type-inputType-postWhereInput">postWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a> | <a href="#type-inputType-postWhereInput">postWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntFilter">IntFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringFilter">StringFilter</a> | String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-BoolFilter">BoolFilter</a> | Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntFilter">IntFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        user </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-UserRelationFilter">UserRelationFilter</a> | <a href="#type-inputType-userWhereInput">userWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postOrderByWithRelationInput">postOrderByWithRelationInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a> | <a href="#type-inputType-SortOrderInput">SortOrderInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a> | <a href="#type-inputType-SortOrderInput">SortOrderInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        user </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postWhereUniqueInput">postWhereUniqueInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a> | <a href="#type-inputType-postWhereInput">postWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a> | <a href="#type-inputType-postWhereInput">postWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringFilter">StringFilter</a> | String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-BoolFilter">BoolFilter</a> | Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntFilter">IntFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        user </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-UserRelationFilter">UserRelationFilter</a> | <a href="#type-inputType-userWhereInput">userWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postOrderByWithAggregationInput">postOrderByWithAggregationInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a> | <a href="#type-inputType-SortOrderInput">SortOrderInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a> | <a href="#type-inputType-SortOrderInput">SortOrderInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCountOrderByAggregateInput">postCountOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postAvgOrderByAggregateInput">postAvgOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postMaxOrderByAggregateInput">postMaxOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postMinOrderByAggregateInput">postMinOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postSumOrderByAggregateInput">postSumOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput</a> | <a href="#type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput</a> | <a href="#type-inputType-postScalarWhereWithAggregatesInput">postScalarWhereWithAggregatesInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntWithAggregatesFilter">IntWithAggregatesFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringWithAggregatesFilter">StringWithAggregatesFilter</a> | String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableWithAggregatesFilter">StringNullableWithAggregatesFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableWithAggregatesFilter">StringNullableWithAggregatesFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-BoolWithAggregatesFilter">BoolWithAggregatesFilter</a> | Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntWithAggregatesFilter">IntWithAggregatesFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userWhereInput">userWhereInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a> | <a href="#type-inputType-userWhereInput">userWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a> | <a href="#type-inputType-userWhereInput">userWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntFilter">IntFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringFilter">StringFilter</a> | String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-PostListRelationFilter">PostListRelationFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userOrderByWithRelationInput">userOrderByWithRelationInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a> | <a href="#type-inputType-SortOrderInput">SortOrderInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postOrderByRelationAggregateInput">postOrderByRelationAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userWhereUniqueInput">userWhereUniqueInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a> | <a href="#type-inputType-userWhereInput">userWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a> | <a href="#type-inputType-userWhereInput">userWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-PostListRelationFilter">PostListRelationFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userOrderByWithAggregationInput">userOrderByWithAggregationInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a> | <a href="#type-inputType-SortOrderInput">SortOrderInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCountOrderByAggregateInput">userCountOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userAvgOrderByAggregateInput">userAvgOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userMaxOrderByAggregateInput">userMaxOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userMinOrderByAggregateInput">userMinOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userSumOrderByAggregateInput">userSumOrderByAggregateInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput</a> | <a href="#type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput</a> | <a href="#type-inputType-userScalarWhereWithAggregatesInput">userScalarWhereWithAggregatesInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntWithAggregatesFilter">IntWithAggregatesFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringWithAggregatesFilter">StringWithAggregatesFilter</a> | String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableWithAggregatesFilter">StringNullableWithAggregatesFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateInput">postCreateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        user </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateNestedOneWithoutPostInput">userCreateNestedOneWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedCreateInput">postUncheckedCreateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpdateInput">postUpdateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        user </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userUpdateOneRequiredWithoutPostNestedInput">userUpdateOneRequiredWithoutPostNestedInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedUpdateInput">postUncheckedUpdateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateManyInput">postCreateManyInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpdateManyMutationInput">postUpdateManyMutationInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedUpdateManyInput">postUncheckedUpdateManyInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userCreateInput">userCreateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateNestedManyWithoutUserInput">postCreateNestedManyWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUncheckedCreateInput">userUncheckedCreateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUncheckedCreateNestedManyWithoutUserInput">postUncheckedCreateNestedManyWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUpdateInput">userUpdateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateManyWithoutUserNestedInput">postUpdateManyWithoutUserNestedInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUncheckedUpdateInput">userUncheckedUpdateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUncheckedUpdateManyWithoutUserNestedInput">postUncheckedUpdateManyWithoutUserNestedInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userCreateManyInput">userCreateManyInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUpdateManyMutationInput">userUpdateManyMutationInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUncheckedUpdateManyInput">userUncheckedUpdateManyInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-IntFilter">IntFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-StringFilter">StringFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringFilter">NestedStringFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-StringNullableFilter">StringNullableFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-BoolFilter">BoolFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BooleanFieldRefInput">BooleanFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-UserRelationFilter">UserRelationFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        is </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        isNot </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-SortOrderInput">SortOrderInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        sort </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        nulls </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NullsOrder">NullsOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCountOrderByAggregateInput">postCountOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postAvgOrderByAggregateInput">postAvgOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postMaxOrderByAggregateInput">postMaxOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postMinOrderByAggregateInput">postMinOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postSumOrderByAggregateInput">postSumOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-IntWithAggregatesFilter">IntWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-NestedIntWithAggregatesFilter">NestedIntWithAggregatesFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedFloatFilter">NestedFloatFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-StringWithAggregatesFilter">StringWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringWithAggregatesFilter">NestedStringWithAggregatesFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringFilter">NestedStringFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringFilter">NestedStringFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-StringNullableWithAggregatesFilter">StringNullableWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringNullableWithAggregatesFilter">NestedStringNullableWithAggregatesFilter</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntNullableFilter">NestedIntNullableFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-BoolWithAggregatesFilter">BoolWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BooleanFieldRefInput">BooleanFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-NestedBoolWithAggregatesFilter">NestedBoolWithAggregatesFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-PostListRelationFilter">PostListRelationFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        every </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        some </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        none </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereInput">postWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postOrderByRelationAggregateInput">postOrderByRelationAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userCountOrderByAggregateInput">userCountOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userAvgOrderByAggregateInput">userAvgOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userMaxOrderByAggregateInput">userMaxOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userMinOrderByAggregateInput">userMinOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userSumOrderByAggregateInput">userSumOrderByAggregateInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-SortOrder">SortOrder</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userCreateNestedOneWithoutPostInput">userCreateNestedOneWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateWithoutPostInput">userCreateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedCreateWithoutPostInput">userUncheckedCreateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connectOrCreate </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateOrConnectWithoutPostInput">userCreateOrConnectWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        set </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        set </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        set </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUpdateOneRequiredWithoutPostNestedInput">userUpdateOneRequiredWithoutPostNestedInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateWithoutPostInput">userCreateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedCreateWithoutPostInput">userUncheckedCreateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connectOrCreate </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateOrConnectWithoutPostInput">userCreateOrConnectWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        upsert </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userUpsertWithoutPostInput">userUpsertWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        update </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userUpdateToOneWithWhereWithoutPostInput">userUpdateToOneWithWhereWithoutPostInput</a> | <a href="#type-inputType-userUpdateWithoutPostInput">userUpdateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedUpdateWithoutPostInput">userUncheckedUpdateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        set </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        increment </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        decrement </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        multiply </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        divide </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateNestedManyWithoutUserInput">postCreateNestedManyWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a> | <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput[]</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connectOrCreate </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput</a> | <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        createMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateManyUserInputEnvelope">postCreateManyUserInputEnvelope</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedCreateNestedManyWithoutUserInput">postUncheckedCreateNestedManyWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a> | <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput[]</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connectOrCreate </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput</a> | <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        createMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateManyUserInputEnvelope">postCreateManyUserInputEnvelope</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpdateManyWithoutUserNestedInput">postUpdateManyWithoutUserNestedInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a> | <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput[]</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connectOrCreate </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput</a> | <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        upsert </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpsertWithWhereUniqueWithoutUserInput">postUpsertWithWhereUniqueWithoutUserInput</a> | <a href="#type-inputType-postUpsertWithWhereUniqueWithoutUserInput">postUpsertWithWhereUniqueWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        createMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateManyUserInputEnvelope">postCreateManyUserInputEnvelope</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        set </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        disconnect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        delete </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        update </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateWithWhereUniqueWithoutUserInput">postUpdateWithWhereUniqueWithoutUserInput</a> | <a href="#type-inputType-postUpdateWithWhereUniqueWithoutUserInput">postUpdateWithWhereUniqueWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        updateMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateManyWithWhereWithoutUserInput">postUpdateManyWithWhereWithoutUserInput</a> | <a href="#type-inputType-postUpdateManyWithWhereWithoutUserInput">postUpdateManyWithWhereWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        deleteMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput</a> | <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedUpdateManyWithoutUserNestedInput">postUncheckedUpdateManyWithoutUserNestedInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a> | <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput[]</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connectOrCreate </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput</a> | <a href="#type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        upsert </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpsertWithWhereUniqueWithoutUserInput">postUpsertWithWhereUniqueWithoutUserInput</a> | <a href="#type-inputType-postUpsertWithWhereUniqueWithoutUserInput">postUpsertWithWhereUniqueWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        createMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateManyUserInputEnvelope">postCreateManyUserInputEnvelope</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        set </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        disconnect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        delete </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        connect </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a> | <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        update </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateWithWhereUniqueWithoutUserInput">postUpdateWithWhereUniqueWithoutUserInput</a> | <a href="#type-inputType-postUpdateWithWhereUniqueWithoutUserInput">postUpdateWithWhereUniqueWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        updateMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateManyWithWhereWithoutUserInput">postUpdateManyWithWhereWithoutUserInput</a> | <a href="#type-inputType-postUpdateManyWithWhereWithoutUserInput">postUpdateManyWithWhereWithoutUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        deleteMany </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput</a> | <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedIntFilter">NestedIntFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedStringFilter">NestedStringFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringFilter">NestedStringFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedBoolFilter">NestedBoolFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BooleanFieldRefInput">BooleanFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedIntWithAggregatesFilter">NestedIntWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-NestedIntWithAggregatesFilter">NestedIntWithAggregatesFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedFloatFilter">NestedFloatFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedFloatFilter">NestedFloatFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Float | <a href="#type-inputType-FloatFieldRefInput">FloatFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        Float
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        Float
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        Float | <a href="#type-inputType-FloatFieldRefInput">FloatFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        Float | <a href="#type-inputType-FloatFieldRefInput">FloatFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        Float | <a href="#type-inputType-FloatFieldRefInput">FloatFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        Float | <a href="#type-inputType-FloatFieldRefInput">FloatFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Float | <a href="#type-inputType-NestedFloatFilter">NestedFloatFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedStringWithAggregatesFilter">NestedStringWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringWithAggregatesFilter">NestedStringWithAggregatesFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringFilter">NestedStringFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringFilter">NestedStringFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedStringNullableWithAggregatesFilter">NestedStringNullableWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        contains </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        startsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        endsWith </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldRefInput">StringFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NestedStringNullableWithAggregatesFilter">NestedStringNullableWithAggregatesFilter</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntNullableFilter">NestedIntNullableFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedStringNullableFilter">NestedStringNullableFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedIntNullableFilter">NestedIntNullableFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        in </td>
      <td class="px-4 py-2 border">
        Int | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        notIn </td>
      <td class="px-4 py-2 border">
        Int | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        lte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gt </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        gte </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldRefInput">IntFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-NestedIntNullableFilter">NestedIntNullableFilter</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-NestedBoolWithAggregatesFilter">NestedBoolWithAggregatesFilter</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        equals </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BooleanFieldRefInput">BooleanFieldRefInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        not </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-NestedBoolWithAggregatesFilter">NestedBoolWithAggregatesFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedIntFilter">NestedIntFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-NestedBoolFilter">NestedBoolFilter</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userCreateWithoutPostInput">userCreateWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUncheckedCreateWithoutPostInput">userUncheckedCreateWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userCreateOrConnectWithoutPostInput">userCreateOrConnectWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereUniqueInput">userWhereUniqueInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateWithoutPostInput">userCreateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedCreateWithoutPostInput">userUncheckedCreateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUpsertWithoutPostInput">userUpsertWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        update </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userUpdateWithoutPostInput">userUpdateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedUpdateWithoutPostInput">userUncheckedUpdateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userCreateWithoutPostInput">userCreateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedCreateWithoutPostInput">userUncheckedCreateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUpdateToOneWithWhereWithoutPostInput">userUpdateToOneWithWhereWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userWhereInput">userWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        data </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-userUpdateWithoutPostInput">userUpdateWithoutPostInput</a> | <a href="#type-inputType-userUncheckedUpdateWithoutPostInput">userUncheckedUpdateWithoutPostInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUpdateWithoutPostInput">userUpdateWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-userUncheckedUpdateWithoutPostInput">userUncheckedUpdateWithoutPostInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateOrConnectWithoutUserInput">postCreateOrConnectWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateManyUserInputEnvelope">postCreateManyUserInputEnvelope</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        data </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateManyUserInput">postCreateManyUserInput</a> | <a href="#type-inputType-postCreateManyUserInput">postCreateManyUserInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        skipDuplicates </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpsertWithWhereUniqueWithoutUserInput">postUpsertWithWhereUniqueWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        update </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateWithoutUserInput">postUpdateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedUpdateWithoutUserInput">postUncheckedUpdateWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        create </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postCreateWithoutUserInput">postCreateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedCreateWithoutUserInput">postUncheckedCreateWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpdateWithWhereUniqueWithoutUserInput">postUpdateWithWhereUniqueWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postWhereUniqueInput">postWhereUniqueInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        data </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateWithoutUserInput">postUpdateWithoutUserInput</a> | <a href="#type-inputType-postUncheckedUpdateWithoutUserInput">postUncheckedUpdateWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpdateManyWithWhereWithoutUserInput">postUpdateManyWithWhereWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        where </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        data </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postUpdateManyMutationInput">postUpdateManyMutationInput</a> | <a href="#type-inputType-postUncheckedUpdateManyWithoutUserInput">postUncheckedUpdateManyWithoutUserInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postScalarWhereInput">postScalarWhereInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        AND </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput</a> | <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        OR </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        NOT </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput</a> | <a href="#type-inputType-postScalarWhereInput">postScalarWhereInput[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntFilter">IntFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringFilter">StringFilter</a> | String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-StringNullableFilter">StringNullableFilter</a> | String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-BoolFilter">BoolFilter</a> | Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        <a href="#type-inputType-IntFilter">IntFilter</a> | Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postCreateManyUserInput">postCreateManyUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUpdateWithoutUserInput">postUpdateWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedUpdateWithoutUserInput">postUncheckedUpdateWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-inputType-postUncheckedUpdateManyWithoutUserInput">postUncheckedUpdateManyWithoutUserInput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int | <a href="#type-inputType-IntFieldUpdateOperationsInput">IntFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-StringFieldUpdateOperationsInput">StringFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String | <a href="#type-inputType-NullableStringFieldUpdateOperationsInput">NullableStringFieldUpdateOperationsInput</a> | Null
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean | <a href="#type-inputType-BoolFieldUpdateOperationsInput">BoolFieldUpdateOperationsInput</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    
            </div>
          </div>
          <div class="ml-4">
            <h3 class="mb-2 text-2xl font-normal" id="output-types">Output Types</h3>
            <div class="ml-4">
              
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-post">post</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        user </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-user">user</a>
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-user">user</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-post">post[]</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserCountOutputType">UserCountOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-AggregatePost">AggregatePost</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostCountAggregateOutputType">PostCountAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostAvgAggregateOutputType">PostAvgAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostSumAggregateOutputType">PostSumAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostMinAggregateOutputType">PostMinAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostMaxAggregateOutputType">PostMaxAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-PostGroupByOutputType">PostGroupByOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostCountAggregateOutputType">PostCountAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostAvgAggregateOutputType">PostAvgAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostSumAggregateOutputType">PostSumAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostMinAggregateOutputType">PostMinAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-PostMaxAggregateOutputType">PostMaxAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-AggregateUser">AggregateUser</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserCountAggregateOutputType">UserCountAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserAvgAggregateOutputType">UserAvgAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserSumAggregateOutputType">UserSumAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserMinAggregateOutputType">UserMinAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserMaxAggregateOutputType">UserMaxAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserGroupByOutputType">UserGroupByOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _count </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserCountAggregateOutputType">UserCountAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _avg </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserAvgAggregateOutputType">UserAvgAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _sum </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserSumAggregateOutputType">UserSumAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _min </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserMinAggregateOutputType">UserMinAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _max </td>
      <td class="px-4 py-2 border">
        <a href="#type-outputType-UserMaxAggregateOutputType">UserMaxAggregateOutputType</a>
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-AffectedRowsOutput">AffectedRowsOutput</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        count </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-PostCountAggregateOutputType">PostCountAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _all </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-PostAvgAggregateOutputType">PostAvgAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Float
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Float
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-PostSumAggregateOutputType">PostSumAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-PostMinAggregateOutputType">PostMinAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-PostMaxAggregateOutputType">PostMaxAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        title </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        content </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        tag </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        published </td>
      <td class="px-4 py-2 border">
        Boolean
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        authorId </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserCountOutputType">UserCountOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        post </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserCountAggregateOutputType">UserCountAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        _all </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        <strong>Yes</strong>
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserAvgAggregateOutputType">UserAvgAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Float
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserSumAggregateOutputType">UserSumAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserMinAggregateOutputType">UserMinAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    <hr class="my-4" />
      <div>
        <h3 class="mb-2 text-xl" id="type-outputType-UserMaxAggregateOutputType">UserMaxAggregateOutputType</h3>
        <table class="table-auto">
          <thead>
            <tr>
              <th class="px-4 py-2 border">Name</th>
              <th class="px-4 py-2 border">Type</th>
              <th class="px-4 py-2 border">Nullable</th>
            </tr>
          </thead>
          <tbody>
          
    <tr>
      <td class="px-4 py-2 border">
        id </td>
      <td class="px-4 py-2 border">
        Int
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        email </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
    <tr>
      <td class="px-4 py-2 border">
        name </td>
      <td class="px-4 py-2 border">
        String
      </td>

      <td class="px-4 py-2 border">
        No
      </td>
    </tr>
    
          </tbody>
        </table>
      </div>
    
            </div>
          </div>
        </div>
      </div>

      </div>
      <div>
      </div>
    </div>
  </body>
</html>
