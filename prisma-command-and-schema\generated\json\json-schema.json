{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"post": {"type": "object", "properties": {"id": {"type": "integer"}, "title": {"type": "string"}, "content": {"type": ["string", "null"]}, "tag": {"type": ["string", "null"]}, "published": {"type": "boolean", "default": false}, "user": {"$ref": "#/definitions/user"}}}, "user": {"type": "object", "properties": {"id": {"type": "integer"}, "email": {"type": "string"}, "name": {"type": ["string", "null"]}, "post": {"type": "array", "items": {"$ref": "#/definitions/post"}}}}}, "type": "object", "properties": {"post": {"$ref": "#/definitions/post"}, "user": {"$ref": "#/definitions/user"}}}