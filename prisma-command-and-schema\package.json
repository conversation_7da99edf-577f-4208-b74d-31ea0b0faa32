{"name": "prisma-command-and-schema", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "prisma": {"seed": "npx ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.8.2", "prisma-docs-generator": "^0.8.0", "prisma-json-schema-generator": "^5.1.5", "ts-node": "^10.9.2", "typescript": "^5.2.2"}, "dependencies": {"@prisma/client": "^5.4.1"}}