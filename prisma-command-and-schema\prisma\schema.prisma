generator client {
  provider = "prisma-client-js"
  output   = "../generated/client"
}

generator docs {
  provider = "node node_modules/prisma-docs-generator"
  output   = "../generated/docs"
}

generator json {
  provider = "prisma-json-schema-generator"
  output   = "../generated/json"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  tag       String? 
  published Boolean @default(false)
  authorId  Int
  user      user    @relation(fields: [authorId], references: [id], map: "Post_authorId_fkey")

  @@index([authorId], map: "Post_authorId_fkey")
}

model user {
  id    Int     @id @default(autoincrement())
  email String  @unique(map: "User_email_key")
  name  String?
  post  post[]
}
